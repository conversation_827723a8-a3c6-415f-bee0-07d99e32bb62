Test name,Eye,<PERSON>,Begin,End,aSP<PERSON>,mSP<PERSON>,<PERSON><PERSON><PERSON>nt,BeatsPrSecond,OppositeDirectionBeatCount,Trace,SetByUser,Label
Center,Left,Up,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>alse,
Center,Left,Down,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Center,Left,Left,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Center,Left,Right,6.6365,16.6365,1.91758773728938,2.91536231020871,3,300,0,<PERSON><PERSON>,False,
Left,Right,Up,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,
Left,Right,Down,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Left,Right,Left,9.7985,19.7985,2.02538445449304,4.40895679971678,4,400,0,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
<PERSON>,Right,<PERSON>,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Right,Left,Up,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
<PERSON>,<PERSON>,<PERSON>,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Right,Left,<PERSON>,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON>e,
<PERSON>,<PERSON>,Right,9.824,19.824,1.40985512288521,1.47481738048953,3,300,0,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Up,<PERSON>,Up,0,0,<PERSON><PERSON>,<PERSON>N,-1,0,-1,Horizontal,False,
Up,Left,Down,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Up,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Up,Left,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Up,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Down,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Right,2.3405,12.3405,2.20157055529374,5.27692419797064,4,400,0,Horizontal,False,
