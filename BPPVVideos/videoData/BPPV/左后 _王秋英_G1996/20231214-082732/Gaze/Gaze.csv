Test name,Eye,Direction,Begin,End,aSPV,mSPV,BeatCount,BeatsPrSecond,OppositeDirectionBeatCount,Trace,SetByUser,Label
Center,Right,Up,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,False,
Center,Right,Down,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,
Center,Right,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Center,Right,Right,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,False,
Left,Left,Up,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,False,
Left,Left,Down,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,Fals<PERSON>,
Left,Left,Left,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,Fals<PERSON>,
Left,Left,Right,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Right,Left,Up,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Right,Left,Down,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,
Right,Left,Left,9.891,19.891,2.34927554367053,4.72995739475929,3,300,0,Horizontal,False,
Right,Left,Right,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Up,Left,Up,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Up,Left,Down,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Up,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Up,Left,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Up,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Down,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
