<PERSON>,<PERSON>,Begin,End,a<PERSON><PERSON>,mSPV,Beat<PERSON>ount,BeatsPrSecond,OppositeDirectionBeatCount,Trace,SetByUser,Label
Left,Up,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>alse,<PERSON>pine
Left,Down,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>
Left,Left,0,0,Na<PERSON>,Na<PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON>alse,Supine
Left,Right,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,<PERSON>pine
Right,Up,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,<PERSON>pine
Right,Down,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,<PERSON>pine
Right,Left,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,Fals<PERSON>,Supine
Right,Right,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>pine
Left,Up,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Sitting
Left,Down,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Sitting
Left,Left,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,False,Sitting
Left,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,Sitting
Right,Up,0,0,NaN,NaN,-1,0,-1,Horizontal,False,Sitting
Right,Down,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,Sitting
Right,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,Sitting
Right,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,Sitting
