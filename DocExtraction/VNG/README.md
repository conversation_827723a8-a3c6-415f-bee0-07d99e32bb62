# VNG报告自动分类系统

## 项目简介

VNG报告自动分类系统是一个智能文档处理工具，专门用于自动识别和分类VNG（视频眼震电图）检查报告。系统能够：

- 🔍 **智能识别VNG报告**：只处理包含特定VNG报告标题的文档
- 📅 **自动提取检查日期**：支持多种日期格式，自动修复格式错误
- 📁 **按年份分类**：将VNG报告按检查年份自动分类到对应文件夹
- 📄 **多格式支持**：完美处理 `.docx`、`.doc`、`.wps` 三种文档格式

## 系统特性

### 🎯 VNG报告识别标准

系统只处理包含以下标题关键词的文档：
- `视频眼震电图（VNG）报告`
- `视频眼震视图（VNG）报告`
- `前庭功能检查报告`
- `VNG`

### 📅 支持的日期格式

系统支持多种检查日期格式，并能自动修复常见错误：

| 格式类型 | 示例 | 说明 |
|---------|------|------|
| 标准格式 | `检查日期：2024-10-14` | 最常见的格式 |
| 中文格式 | `检查日期: 2024年10月14日` | 中文日期表示 |
| 空格变化 | `检查日期 2024-10-14` | 处理各种空格情况 |
| 双横线修复 | `2023--10-10` → `2023-10-10` | 自动修复格式错误 |
| 特殊格式 | `201910-09` → `2019-10-09` | 处理年月连写格式 |
| 纯日期 | `2024-10-14` | 文档中的标准日期 |

### 📊 处理效果

在实际测试中，系统处理了7,307个文档：
- ✅ **成功分类**: 6,876个VNG报告 (94.1%)
- 📂 **按年份分类**: 2018-2024年，共7个年份
- 🗂️ **非VNG文档**: 431个文档放入undefined文件夹

## 安装要求

### Python环境
- Python 3.6+

### 依赖包
```bash
pip install python-docx olefile
```

## 使用方法

### 1. 快速开始

```bash
# 进入代码目录
cd DocExtraction/VNG/code

# 运行VNG报告分类
python final_vng_classification.py
```

### 2. 自定义参数

```bash
python final_vng_classification.py \
    --source-dir /path/to/source/documents \
    --target-dir /path/to/output/directory \
    --debug
```

### 3. 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--source-dir` | 源文档目录路径 | `../data/VNG检查报告2024.11.17` |
| `--target-dir` | 输出分类目录路径 | `../data/final_vng_classified` |
| `--debug` | 显示详细调试信息 | False |

## 输出结构

分类完成后，会生成以下目录结构：

```
final_vng_classified/
├── 2018/          # 2018年的VNG报告
│   ├── 146 梁趣轩.docx
│   ├── 147 钟煜基.docx
│   └── ...
├── 2019/          # 2019年的VNG报告
│   ├── 1001 凌梓晴.wps
│   ├── 1106 张晓桥.wps
│   └── ...
├── 2020/          # 2020年的VNG报告
├── 2021/          # 2021年的VNG报告
├── 2022/          # 2022年的VNG报告
├── 2023/          # 2023年的VNG报告
├── 2024/          # 2024年的VNG报告
└── undefined/     # 非VNG报告或无法提取日期的文档
    ├── 前庭报告模板 DOCX 文档.docx
    ├── 16 马耀锦.docx
    └── ...
```

## 核心代码文件

### 1. `classify_documents_by_year.py`
核心逻辑文件，包含：
- VNG报告识别函数
- 多格式文档内容提取
- 智能日期提取和验证
- 年份提取功能

### 2. `final_vng_classification.py`
主要执行脚本，包含：
- 完整的分类流程
- 命令行参数处理
- 统计报告生成
- 错误处理机制

## 技术实现

### 文档内容提取

#### DOCX文件
```python
from docx import Document
doc = Document(file_path)
# 提取段落和表格内容
```

#### DOC/WPS文件
```python
import olefile
ole = olefile.OleFileIO(file_path)
# 从WordDocument流中提取文本
```

### VNG报告验证
```python
def is_valid_vng_report(full_text, debug=False):
    valid_titles = [
        '视频眼震电图（VNG）报告',
        '视频眼震视图（VNG）报告', 
        '前庭功能检查报告',
        'VNG'
    ]
    return any(title in full_text for title in valid_titles)
```

### 日期提取与验证
```python
def extract_date_from_document_content(file_path, debug=False):
    # 1. 提取文档内容
    # 2. 验证是否为VNG报告
    # 3. 使用正则表达式提取日期
    # 4. 验证日期合理性
    # 5. 返回标准化日期格式
```

## 常见问题

### Q: 为什么某些文档没有被分类？
A: 可能的原因：
1. 文档不包含VNG报告标题关键词
2. 文档中没有检查日期字段
3. 检查日期格式无法识别
4. 年份不在合理范围内(2010-2025)

### Q: 如何处理undefined文件夹中的文档？
A: undefined文件夹中的文档需要手动检查：
1. 确认是否为VNG报告
2. 检查是否有检查日期
3. 如果是VNG报告但缺少日期，可以手动添加日期后重新处理

### Q: 支持哪些文档格式？
A: 目前支持：
- `.docx` - Microsoft Word 2007+格式
- `.doc` - Microsoft Word 97-2003格式  
- `.wps` - 金山WPS文档格式

## 更新日志

### v1.0.0 (2025-01-02)
- ✅ 实现VNG报告智能识别
- ✅ 支持多种日期格式提取
- ✅ 支持.docx、.doc、.wps格式
- ✅ 自动格式错误修复
- ✅ 按年份自动分类
- ✅ 94.1%的分类成功率

## 许可证

本项目仅供内部使用。

## 联系方式

如有问题或建议，请联系开发团队。
