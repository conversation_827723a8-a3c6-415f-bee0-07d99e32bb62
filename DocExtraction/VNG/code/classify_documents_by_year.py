#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG检查报告文档按年份分类工具

此脚本用于将VNG检查报告文档按照检查日期的年份进行分类，
将文档复制到对应的年份文件夹中。

功能：
- 支持处理 .docx、.doc、.wps 格式的文档
- 从文档中提取检查日期
- 按年份创建文件夹（如2018、2019等）
- 将文档复制到对应年份的文件夹中
- 生成处理报告

依赖包：
    pip install python-docx docx2txt

使用方法:
    python classify_documents_by_year.py [--source-dir SOURCE_DIR] [--target-dir TARGET_DIR] [--debug]
    
    参数：
    --source-dir     - 源文档目录路径，默认为当前目录下的data/VNG检查报告2024.11.17
    --target-dir     - 目标分类目录路径，默认为源目录同级的classified_by_year
    --debug          - 显示调试信息
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import shutil
import argparse
from pathlib import Path
from datetime import datetime
import traceback

# 导入现有的文档信息提取功能
sys.path.append(os.path.dirname(__file__))
from extract_patient_info import extract_patient_info


def is_valid_date_format(date_str):
    """
    验证日期格式是否合理

    参数:
        date_str (str): 日期字符串

    返回:
        bool: 是否为有效日期格式
    """
    if not date_str:
        return False

    # 尝试提取年份并验证
    year_match = re.search(r'(\d{4})', date_str)
    if year_match:
        year = int(year_match.group(1))
        return 1990 <= year <= 2030

    return False


def has_complete_year(date_str):
    """
    检查日期字符串是否包含完整的4位年份

    参数:
        date_str (str): 日期字符串

    返回:
        bool: 是否包含完整的4位年份
    """
    if not date_str:
        return False

    # 检查是否包含4位连续数字作为年份
    year_match = re.search(r'\b(\d{4})\b', date_str)
    if year_match:
        year = int(year_match.group(1))
        # 年份必须在合理范围内
        return 1990 <= year <= 2030

    return False


def is_reasonable_check_date(date_str):
    """
    检查日期是否是合理的检查日期

    参数:
        date_str (str): 日期字符串

    返回:
        bool: 是否是合理的检查日期
    """
    if not date_str:
        return False

    try:
        from datetime import datetime

        # 提取年、月、日
        year_match = re.search(r'(\d{4})', date_str)
        if not year_match:
            return False

        year = int(year_match.group(1))

        # 年份必须在合理范围内（2010-2025，因为这是医疗检查报告）
        if not (2010 <= year <= 2025):
            return False

        # 尝试解析完整日期以验证月日的合理性
        date_formats = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            r'(\d{4})/(\d{1,2})/(\d{1,2})',
            r'(\d{4})年(\d{1,2})月(\d{1,2})日'
        ]

        for pattern in date_formats:
            match = re.search(pattern, date_str)
            if match:
                year, month, day = map(int, match.groups())
                # 验证月份和日期的合理性
                if 1 <= month <= 12 and 1 <= day <= 31:
                    # 进一步验证日期的有效性
                    try:
                        datetime(year, month, day)
                        return True
                    except ValueError:
                        continue

        return False

    except Exception:
        return False


def extract_date_from_filename(filename):
    """
    从文件名中提取日期信息

    参数:
        filename (str): 文件名

    返回:
        str: 提取的日期字符串，如果无法提取则返回None
    """
    # 从文件名中提取日期的模式
    filename_patterns = [
        r'(\d{4}-\d{1,2}-\d{1,2})',  # 2023-01-01
        r'(\d{4}\d{2}\d{2})',        # 20230101
        r'(\d{4}年\d{1,2}月\d{1,2}日)',  # 2023年01月01日
    ]

    for pattern in filename_patterns:
        match = re.search(pattern, filename)
        if match:
            date_str = match.group(1)
            if is_valid_date_format(date_str):
                return date_str

    return None


def extract_year_from_date(date_str):
    """
    从日期字符串中提取年份

    参数:
        date_str (str): 日期字符串

    返回:
        str: 年份字符串，如果无法提取则返回None
    """
    if not date_str:
        return None

    # 匹配各种日期格式中的年份
    year_patterns = [
        r'(\d{4})[-/年]',  # 2023-01-01, 2023/01/01, 2023年01月01日
        r'^(\d{4})',       # 以4位数字开头
        r'(\d{4})\.',      # 2023.01.01
        r'(\d{4})\s',      # 2023 01 01
    ]

    for pattern in year_patterns:
        match = re.search(pattern, date_str)
        if match:
            year = match.group(1)
            # 验证年份是否合理（1990-2030）
            if 1990 <= int(year) <= 2030:
                return year

    return None


def extract_date_from_document_content(file_path, debug=False):
    """
    直接从文档内容中提取日期，支持多种文档格式

    参数:
        file_path (str): 文档文件路径
        debug (bool): 是否显示调试信息

    返回:
        str: 提取的日期字符串，如果无法提取则返回空字符串
    """
    try:
        import docx
        from docx import Document
        import zipfile

        # 获取文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.docx':
            # 处理.docx文件
            try:
                doc = Document(file_path)
                full_text = ""

                # 提取段落文本
                for paragraph in doc.paragraphs:
                    full_text += paragraph.text + "\n"

                # 提取表格文本
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            full_text += cell.text + " "
                    full_text += "\n"

            except Exception as e:
                if debug:
                    print(f"  无法读取.docx文件: {e}")
                return ""

        elif file_ext in ['.doc', '.wps']:
            # 对于.doc和.wps文件，尝试多种方法
            full_text = ""

            # 方法1: 对于.wps和.doc文件，尝试使用olefile处理OLE复合文档
            if file_ext in ['.wps', '.doc']:
                try:
                    import olefile
                    if olefile.isOleFile(file_path):
                        ole = olefile.OleFileIO(file_path)
                        # 列出所有可用的流
                        streams = ole.listdir()
                        if debug:
                            print(f"  OLE文件中的流: {streams}")

                        # 按优先级尝试读取不同的流，优先读取文档内容流
                        priority_streams = ['WordDocument', '1Table', '0Table', 'Data']
                        other_streams = ['/'.join(stream_info) if isinstance(stream_info, list) else str(stream_info)
                                       for stream_info in streams]

                        # 首先尝试优先级高的流
                        for stream_name in priority_streams:
                            if stream_name in [s.replace('\x05', '') for s in other_streams]:
                                try:
                                    if ole.exists(stream_name):
                                        stream_data = ole.openstream(stream_name).read()
                                        # 尝试解码为文本
                                        text_data = stream_data.decode('utf-8', errors='ignore')
                                        # 过滤掉大部分二进制字符，只保留可读文本
                                        clean_text = ''.join(char for char in text_data if char.isprintable() or char.isspace())
                                        if len(clean_text.strip()) > 100:  # 需要更多文本才认为有效
                                            full_text += clean_text
                                            if debug:
                                                print(f"  使用olefile从{stream_name}流成功读取.wps文件")
                                            break
                                except Exception as stream_error:
                                    if debug:
                                        print(f"  无法读取优先流{stream_name}: {stream_error}")
                                    continue

                        # 如果优先流没有足够内容，尝试其他流
                        if len(full_text.strip()) < 100:
                            for stream_info in streams:
                                stream_name = '/'.join(stream_info) if isinstance(stream_info, list) else str(stream_info)
                                # 跳过系统信息流
                                if any(skip in stream_name.lower() for skip in ['summary', 'information', 'compobj']):
                                    continue
                                try:
                                    if ole.exists(stream_name):
                                        stream_data = ole.openstream(stream_name).read()
                                        text_data = stream_data.decode('utf-8', errors='ignore')
                                        clean_text = ''.join(char for char in text_data if char.isprintable() or char.isspace())
                                        if len(clean_text.strip()) > 100:
                                            full_text += clean_text
                                            if debug:
                                                print(f"  使用olefile从{stream_name}流成功读取.wps文件")
                                            break
                                except Exception as stream_error:
                                    if debug:
                                        print(f"  无法读取流{stream_name}: {stream_error}")
                                    continue
                        ole.close()
                    else:
                        if debug:
                            print(f"  文件不是有效的OLE文件")
                except ImportError:
                    if debug:
                        print(f"  olefile未安装")
                except Exception as e:
                    if debug:
                        print(f"  olefile无法读取.wps文件: {e}")

            # 方法2: 尝试使用docx2txt
            if not full_text:
                try:
                    import docx2txt
                    full_text = docx2txt.process(file_path)
                    if debug:
                        print(f"  使用docx2txt成功读取{file_ext}文件")
                except ImportError:
                    if debug:
                        print(f"  docx2txt未安装")
                except Exception as e:
                    if debug:
                        print(f"  docx2txt无法读取{file_ext}文件: {e}")

            # 方法3: 如果docx2txt失败，尝试使用python-docx（可能对某些.doc文件有效）
            if not full_text:
                try:
                    doc = Document(file_path)
                    for paragraph in doc.paragraphs:
                        full_text += paragraph.text + "\n"
                    for table in doc.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                full_text += cell.text + " "
                        full_text += "\n"
                    if debug:
                        print(f"  使用python-docx成功读取{file_ext}文件")
                except Exception as e:
                    if debug:
                        print(f"  python-docx无法读取{file_ext}文件: {e}")

            # 方法3: 尝试使用subprocess调用系统工具
            if not full_text and file_ext == '.doc':
                try:
                    import subprocess
                    # 尝试使用catdoc（如果系统中安装了）
                    result = subprocess.run(['catdoc', file_path],
                                          capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        full_text = result.stdout
                        if debug:
                            print(f"  使用catdoc成功读取{file_ext}文件")
                    else:
                        if debug:
                            print(f"  catdoc执行失败: {result.stderr}")
                except FileNotFoundError:
                    if debug:
                        print(f"  catdoc未安装")
                except Exception as e:
                    if debug:
                        print(f"  catdoc执行出错: {e}")

            # 方法4: 如果是.wps文件，尝试作为zip文件处理
            if not full_text and file_ext == '.wps':
                try:
                    import zipfile
                    import xml.etree.ElementTree as ET

                    with zipfile.ZipFile(file_path, 'r') as zip_file:
                        # WPS文件可能有不同的内部结构
                        for name in zip_file.namelist():
                            if 'content' in name.lower() or 'document' in name.lower():
                                content = zip_file.read(name)
                                # 尝试解析XML
                                try:
                                    root = ET.fromstring(content)
                                    full_text = ET.tostring(root, encoding='unicode', method='text')
                                    if debug:
                                        print(f"  从WPS文件的{name}中提取文本")
                                    break
                                except:
                                    continue
                except Exception as e:
                    if debug:
                        print(f"  无法作为zip处理WPS文件: {e}")

            if not full_text:
                if debug:
                    print(f"  所有方法都无法读取{file_ext}文件")
                return ""
        else:
            if debug:
                print(f"  不支持的文件格式: {file_ext}")
            return ""

        if debug:
            print(f"  提取的文本长度: {len(full_text)}")
            print(f"  文本前200字符: {full_text[:200]}")

        # 改进的日期提取正则表达式 - 处理各种空格和格式变化
        date_patterns = [
            # 优先匹配明确标注为"检查日期"的格式，处理各种空格情况
            r'检查日期\s*[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'检查日期\s*[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日)',
            r'检\s*查\s*日\s*期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检查时间\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',

            # 处理检查日期后有多个空格或其他字符的情况
            r'检查日期\s*[：:]\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检\s*查\s*日\s*期\s*[：:]\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',

            # 处理不完整年份的情况（如201-5-8应该是2018-5-8，201910-09应该是2019-10-09）
            r'检查日期\s*[：:]\s*(20\d)[-/](\d{1,2})[-/](\d{1,2})',
            r'检\s*查\s*日\s*期\s*[：:]\s*(20\d)[-/](\d{1,2})[-/](\d{1,2})',
            r'(20\d{3})-(\d{1,2})-(\d{1,2})',  # 处理201910-09这种格式 -> 2019-10-09
            r'(20\d{3})/(\d{1,2})/(\d{1,2})',  # 处理201910/09这种格式 -> 2019/10/09

            # 处理检查日期和冒号之间有空格的情况
            r'检查日期\s+[：:]\s*(\d{4}[.\-/年]\d{1,2}[.\-/月]\d{1,2}[日]?)',
            r'检\s*查\s*日\s*期\s+[：:]\s*(\d{4}[.\-/年]\d{1,2}[.\-/月]\d{1,2}[日]?)',

            # 处理没有冒号的情况
            r'检查日期\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检\s*查\s*日\s*期\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',

            # 特殊格式处理
            r'(20\d{2})(\d{1,2})-(\d{1,2})',  # 201910-09 -> 2019-10-09

            # 文档中常见的标准日期格式（作为备选，但需要验证合理性）
            r'(\d{4}-\d{1,2}-\d{1,2})',  # 2024-10-14
            r'(\d{4}/\d{1,2}/\d{1,2})',  # 2024/10/14
            r'(\d{4}年\d{1,2}月\d{1,2}日)',  # 2024年10月14日
        ]

        for pattern in date_patterns:
            match = re.search(pattern, full_text)
            if match:
                # 处理不完整年份的特殊情况
                if len(match.groups()) == 3:  # 三个捕获组的情况（年、月、日分开）
                    year_part, month, day = match.groups()

                    # 处理20191-09这种格式（年份是5位数）
                    if len(year_part) == 5 and year_part.startswith('20'):
                        # 20191 -> 2019年1月，但这里应该是2019年10月
                        year = year_part[:4]  # 2019
                        month = year_part[4:] + month  # 1 + 0 = 10
                        date_str = f"{year}-{month}-{day}"
                    else:
                        # 修复不完整的年份（如201 -> 2018）
                        if len(year_part) == 3 and year_part.startswith('20'):
                            # 根据上下文推断完整年份，这里假设是2018年
                            year_part = year_part + '8'  # 201 -> 2018
                        date_str = f"{year_part}-{month}-{day}"
                else:
                    date_str = match.group(1)

                # 验证日期格式的合理性 - 必须有完整的4位年份且是合理的检查日期
                if is_valid_date_format(date_str) and has_complete_year(date_str) and is_reasonable_check_date(date_str):
                    if debug:
                        print(f"  找到日期: {date_str} (使用模式: {pattern})")
                    return date_str
                elif debug:
                    print(f"  找到但格式无效的日期: {date_str}")

        # 不再从文件名中推断日期 - 严格要求文档内容中有明确的检查日期

        if debug:
            print("  未找到匹配的日期格式")

        return ""

    except Exception as e:
        if debug:
            print(f"  提取日期时发生错误: {e}")
        return ""


def get_supported_files(directory):
    """
    获取目录下所有支持的文档文件
    
    参数:
        directory (str): 目录路径
        
    返回:
        list: 文件路径列表
    """
    supported_extensions = ['.docx', '.doc', '.wps']
    files = []
    
    for ext in supported_extensions:
        pattern = os.path.join(directory, f"*{ext}")
        import glob
        files.extend(glob.glob(pattern))
    
    return sorted(files)


def classify_documents_by_year(source_dir, target_dir, debug=False):
    """
    按年份分类文档
    
    参数:
        source_dir (str): 源文档目录
        target_dir (str): 目标分类目录
        debug (bool): 是否显示调试信息
        
    返回:
        dict: 分类结果统计
    """
    # 确保目录存在
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在 - {source_dir}")
        return None
    
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"创建目标目录: {target_dir}")
    
    # 获取所有支持的文档文件
    files = get_supported_files(source_dir)
    if not files:
        print(f"在目录 {source_dir} 中未找到支持的文档文件")
        return None
    
    print(f"找到 {len(files)} 个文档文件")
    
    # 分类统计
    classification_stats = {
        'total_files': len(files),
        'processed_files': 0,
        'failed_files': 0,
        'year_counts': {},
        'failed_list': [],
        'no_date_files': [],
        'undefined_files': 0,
        'format_stats': {}
    }
    
    for i, file_path in enumerate(files, 1):
        filename = os.path.basename(file_path)
        print(f"[{i}/{len(files)}] 处理文件: {filename}")
        
        try:
            # 首先尝试使用原有的extract_patient_info方法
            info = extract_patient_info(file_path, debug=debug)
            check_date = info.get('检查日期', '')

            # 如果原有方法没有提取到日期，尝试直接从文档内容提取
            if not check_date:
                if debug:
                    print(f"  原方法未提取到日期，尝试直接从文档内容提取...")
                check_date = extract_date_from_document_content(file_path, debug=debug)

            if debug:
                print(f"  最终提取的检查日期: {check_date}")

            # 提取年份
            year = extract_year_from_date(check_date)
            
            if year:
                # 创建年份目录
                year_dir = os.path.join(target_dir, year)
                if not os.path.exists(year_dir):
                    os.makedirs(year_dir)
                    print(f"  创建年份目录: {year}")
                
                # 复制文件到年份目录
                target_file = os.path.join(year_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(year_dir, new_filename)
                        counter += 1
                    print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                print(f"  复制到: {year}/{os.path.basename(target_file)}")
                
                # 更新统计
                classification_stats['year_counts'][year] = classification_stats['year_counts'].get(year, 0) + 1
                classification_stats['processed_files'] += 1
                
            else:
                print(f"  警告: 无法从文档中提取有效的检查日期，将复制到undefined文件夹")

                # 创建undefined目录
                undefined_dir = os.path.join(target_dir, "undefined")
                if not os.path.exists(undefined_dir):
                    os.makedirs(undefined_dir)
                    print(f"  创建undefined目录: {undefined_dir}")

                # 复制文件到undefined目录
                target_file = os.path.join(undefined_dir, filename)

                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(undefined_dir, new_filename)
                        counter += 1
                    print(f"  文件重名，重命名为: {os.path.basename(target_file)}")

                shutil.copy2(file_path, target_file)
                print(f"  复制到: undefined/{os.path.basename(target_file)}")

                classification_stats['no_date_files'].append(filename)
                classification_stats['undefined_files'] += 1
                
        except Exception as e:
            print(f"  错误: 处理文件时发生异常: {e}")
            if debug:
                print(traceback.format_exc())
            classification_stats['failed_list'].append((filename, str(e)))
            classification_stats['failed_files'] += 1
    
    return classification_stats


def print_classification_report(stats):
    """
    打印分类结果报告

    参数:
        stats (dict): 分类统计结果
    """
    print("\n" + "="*50)
    print("文档分类结果报告")
    print("="*50)

    print(f"总文件数: {stats['total_files']}")
    print(f"成功分类: {stats['processed_files']}")
    print(f"移至undefined: {stats.get('undefined_files', 0)}")
    print(f"处理异常: {len(stats.get('failed_list', []))}")

    # 计算成功率
    success_rate = (stats['processed_files'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    print(f"成功分类率: {success_rate:.1f}%")

    if stats['year_counts']:
        print("\n按年份分类统计:")
        for year in sorted(stats['year_counts'].keys()):
            count = stats['year_counts'][year]
            print(f"  {year}年: {count} 个文件")

    # 按文件格式统计失败情况
    if stats.get('format_stats'):
        print("\n按文件格式统计:")
        for ext, format_stat in stats['format_stats'].items():
            total = format_stat['total']
            success = format_stat['success']
            rate = (success / total * 100) if total > 0 else 0
            print(f"  {ext}: {success}/{total} ({rate:.1f}%)")

    if stats['no_date_files']:
        print(f"\n无法提取日期的文件 ({len(stats['no_date_files'])} 个):")
        for filename in stats['no_date_files'][:10]:  # 只显示前10个
            print(f"  - {filename}")
        if len(stats['no_date_files']) > 10:
            print(f"  ... 还有 {len(stats['no_date_files']) - 10} 个文件")

    if stats['failed_list']:
        print(f"\n处理异常的文件 ({len(stats['failed_list'])} 个):")
        for filename, error in stats['failed_list'][:5]:  # 只显示前5个
            print(f"  - {filename}: {error}")
        if len(stats['failed_list']) > 5:
            print(f"  ... 还有 {len(stats['failed_list']) - 5} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VNG检查报告文档按年份分类工具')

    # 默认路径 - 使用绝对路径
    default_source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/VNG检查报告2024.11.17"
    default_target_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/classified_by_year"
    
    parser.add_argument('--source-dir', 
                       default=str(default_source_dir),
                       help=f'源文档目录路径 (默认: {default_source_dir})')
    parser.add_argument('--target-dir',
                       default=str(default_target_dir), 
                       help=f'目标分类目录路径 (默认: {default_target_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')
    
    args = parser.parse_args()
    
    print("VNG检查报告文档按年份分类工具")
    print(f"源目录: {args.source_dir}")
    print(f"目标目录: {args.target_dir}")
    print("-" * 50)
    
    # 执行分类
    stats = classify_documents_by_year(args.source_dir, args.target_dir, args.debug)
    
    if stats:
        print_classification_report(stats)
    else:
        print("分类处理失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
