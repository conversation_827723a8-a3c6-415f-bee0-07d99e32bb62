#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终VNG报告分类脚本

此脚本使用完整的VNG报告验证和日期提取逻辑重新分类所有文档：
1. 只处理包含VNG报告标题的文档
2. 提取检查日期并按年份分类
3. 其他文档放入undefined文件夹

使用方法:
    python final_vng_classification.py [--source-dir SOURCE_DIR] [--target-dir TARGET_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import shutil
import argparse
from pathlib import Path
import traceback

# 导入完整的文档信息提取功能
sys.path.append(os.path.dirname(__file__))
from classify_documents_by_year import extract_date_from_document_content, extract_year_from_date


def final_vng_classification(source_dir, target_dir, debug=False):
    """
    最终VNG报告分类
    
    参数:
        source_dir (str): 源文件目录
        target_dir (str): 目标分类目录
        debug (bool): 是否显示调试信息
        
    返回:
        dict: 分类结果统计
    """
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在 - {source_dir}")
        return None
    
    # 清空目标目录
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    os.makedirs(target_dir)
    
    # 创建undefined目录
    undefined_dir = os.path.join(target_dir, 'undefined')
    os.makedirs(undefined_dir)
    
    # 获取所有文件
    files = []
    for ext in ['.docx', '.doc', '.wps']:
        files.extend([f for f in os.listdir(source_dir) if f.endswith(ext)])
    
    if not files:
        print(f"在目录 {source_dir} 中未找到文件")
        return None
    
    print(f"找到 {len(files)} 个文件需要分类")
    
    # 处理统计
    stats = {
        'total_files': len(files),
        'valid_vng_reports': 0,
        'successfully_classified': 0,
        'moved_to_undefined': 0,
        'year_counts': {},
        'failed_list': []
    }
    
    for i, filename in enumerate(files, 1):
        file_path = os.path.join(source_dir, filename)
        if i % 500 == 0 or i <= 10:
            print(f"[{i}/{len(files)}] 处理文件: {filename}")
        
        try:
            # 使用完整的VNG报告验证和日期提取
            check_date = extract_date_from_document_content(file_path, debug=debug and i <= 5)
            year = extract_year_from_date(check_date) if check_date else None
            
            if year and check_date:
                # 这是有效的VNG报告且有检查日期
                stats['valid_vng_reports'] += 1
                
                # 创建年份目录
                year_dir = os.path.join(target_dir, year)
                if not os.path.exists(year_dir):
                    os.makedirs(year_dir)
                    if i <= 10:
                        print(f"  创建年份目录: {year}")
                
                # 移动文件到年份目录
                target_file = os.path.join(year_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(year_dir, new_filename)
                        counter += 1
                    if i <= 10:
                        print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                if i <= 10:
                    print(f"  分类到: {year}/{os.path.basename(target_file)}")
                
                # 更新统计
                stats['year_counts'][year] = stats['year_counts'].get(year, 0) + 1
                stats['successfully_classified'] += 1
                
            else:
                # 不是有效的VNG报告或没有检查日期，移动到undefined目录
                target_file = os.path.join(undefined_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(undefined_dir, new_filename)
                        counter += 1
                    if i <= 10:
                        print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                if i <= 10:
                    print(f"  移动到: undefined/{os.path.basename(target_file)}")
                stats['moved_to_undefined'] += 1
                
        except Exception as e:
            if i <= 10:
                print(f"  错误: 处理文件时发生异常: {e}")
            if debug:
                print(traceback.format_exc())
            stats['failed_list'].append((filename, str(e)))
            
            # 异常文件也移动到undefined
            try:
                target_file = os.path.join(undefined_dir, filename)
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(undefined_dir, new_filename)
                        counter += 1
                shutil.copy2(file_path, target_file)
                stats['moved_to_undefined'] += 1
            except:
                pass
    
    return stats


def print_final_classification_report(stats):
    """
    打印最终分类结果报告
    
    参数:
        stats (dict): 处理统计结果
    """
    print("\n" + "="*60)
    print("最终VNG报告分类结果")
    print("="*60)
    
    print(f"总文件数: {stats['total_files']:,}")
    print(f"有效VNG报告: {stats['valid_vng_reports']:,}")
    print(f"成功分类: {stats['successfully_classified']:,}")
    print(f"移至undefined: {stats['moved_to_undefined']:,}")
    
    # 计算成功率
    vng_rate = (stats['valid_vng_reports'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    success_rate = (stats['successfully_classified'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    
    print(f"\nVNG报告比例: {vng_rate:.1f}%")
    print(f"分类成功率: {success_rate:.1f}%")
    
    if stats['year_counts']:
        print("\n按年份分类统计:")
        for year in sorted(stats['year_counts'].keys()):
            count = stats['year_counts'][year]
            print(f"  {year}年: {count:,} 个文件")
    
    if stats['failed_list']:
        print(f"\n处理异常的文件 ({len(stats['failed_list'])} 个):")
        for filename, error in stats['failed_list'][:5]:
            print(f"  - {filename}: {error}")
        if len(stats['failed_list']) > 5:
            print(f"  ... 还有 {len(stats['failed_list']) - 5} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='最终VNG报告分类')
    
    # 默认路径
    default_source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/VNG检查报告2024.11.17"
    default_target_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"
    
    parser.add_argument('--source-dir', 
                       default=default_source_dir,
                       help=f'源文件目录路径 (默认: {default_source_dir})')
    parser.add_argument('--target-dir',
                       default=default_target_dir, 
                       help=f'目标分类目录路径 (默认: {default_target_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')
    
    args = parser.parse_args()
    
    print("最终VNG报告分类")
    print("只处理包含VNG报告标题的文档，其他文档放入undefined")
    print(f"源目录: {args.source_dir}")
    print(f"目标目录: {args.target_dir}")
    print("-" * 60)
    
    # 执行最终分类
    stats = final_vng_classification(args.source_dir, args.target_dir, args.debug)
    
    if stats:
        print_final_classification_report(stats)
    else:
        print("最终分类失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
