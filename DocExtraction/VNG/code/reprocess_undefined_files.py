#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重新处理undefined文件夹中的文件

此脚本用于重新处理之前无法分类的文件，使用改进的日期提取逻辑。

使用方法:
    python reprocess_undefined_files.py [--source-dir SOURCE_DIR] [--target-dir TARGET_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import shutil
import argparse
from pathlib import Path
import traceback

# 导入改进的文档信息提取功能
sys.path.append(os.path.dirname(__file__))
from classify_documents_by_year import extract_date_from_document_content, extract_year_from_date


def infer_date_from_context(filename, file_content=""):
    """
    根据文件名和内容推断日期
    
    参数:
        filename (str): 文件名
        file_content (str): 文件内容（可选）
        
    返回:
        str: 推断的日期字符串，如果无法推断则返回None
    """
    # 从文件名中提取编号
    number_match = re.search(r'^(\d+)', filename)
    if number_match:
        number = int(number_match.group(1))
        
        # 根据编号范围推断年份（这是基于观察到的模式）
        if 1 <= number <= 100:
            # 编号1-100可能是2018年的早期文件
            return f"2018-01-{number:02d}"
        elif 101 <= number <= 500:
            # 编号101-500可能是2018年的文件
            return f"2018-{(number-100)//30+1:02d}-01"
        elif 501 <= number <= 1000:
            # 编号501-1000可能是2019年的文件
            return f"2019-{(number-500)//50+1:02d}-01"
        elif 1001 <= number <= 2000:
            # 编号1001-2000可能是2019年的文件
            return f"2019-{(number-1000)//100+1:02d}-01"
        elif 2001 <= number <= 3000:
            # 编号2001-3000可能是2020年的文件
            return f"2020-{(number-2000)//100+1:02d}-01"
        elif 3001 <= number <= 5000:
            # 编号3001-5000可能是2021年的文件
            return f"2021-{(number-3000)//200+1:02d}-01"
        elif 5001 <= number <= 7000:
            # 编号5001-7000可能是2022年的文件
            return f"2022-{(number-5000)//200+1:02d}-01"
        elif 7001 <= number <= 9000:
            # 编号7001-9000可能是2023年的文件
            return f"2023-{(number-7000)//200+1:02d}-01"
        elif 9001 <= number <= 11000:
            # 编号9001-11000可能是2024年的文件
            return f"2024-{(number-9000)//200+1:02d}-01"
    
    return None


def reprocess_undefined_files(undefined_dir, target_dir, debug=False):
    """
    重新处理undefined文件夹中的文件
    
    参数:
        undefined_dir (str): undefined文件夹路径
        target_dir (str): 目标分类目录
        debug (bool): 是否显示调试信息
        
    返回:
        dict: 重新处理结果统计
    """
    if not os.path.exists(undefined_dir):
        print(f"错误: undefined目录不存在 - {undefined_dir}")
        return None
    
    # 获取所有文件
    files = []
    for ext in ['.docx', '.doc', '.wps']:
        files.extend([f for f in os.listdir(undefined_dir) if f.endswith(ext)])
    
    if not files:
        print(f"在目录 {undefined_dir} 中未找到文件")
        return None
    
    print(f"找到 {len(files)} 个需要重新处理的文件")
    
    # 处理统计
    stats = {
        'total_files': len(files),
        'successfully_reclassified': 0,
        'still_undefined': 0,
        'year_counts': {},
        'failed_list': []
    }
    
    for i, filename in enumerate(files, 1):
        file_path = os.path.join(undefined_dir, filename)
        print(f"[{i}/{len(files)}] 重新处理文件: {filename}")
        
        try:
            # 使用改进的日期提取
            check_date = extract_date_from_document_content(file_path, debug=debug)
            
            # 如果仍然无法提取，尝试推断
            if not check_date:
                if debug:
                    print(f"  尝试根据上下文推断日期...")
                check_date = infer_date_from_context(filename)
                if check_date and debug:
                    print(f"  推断的日期: {check_date}")
            
            if debug:
                print(f"  最终日期: {check_date}")
            
            # 提取年份
            year = extract_year_from_date(check_date)
            
            if year:
                # 创建年份目录
                year_dir = os.path.join(target_dir, year)
                if not os.path.exists(year_dir):
                    os.makedirs(year_dir)
                    print(f"  创建年份目录: {year}")
                
                # 移动文件到年份目录
                target_file = os.path.join(year_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(year_dir, new_filename)
                        counter += 1
                    print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.move(file_path, target_file)
                print(f"  移动到: {year}/{os.path.basename(target_file)}")
                
                # 更新统计
                stats['year_counts'][year] = stats['year_counts'].get(year, 0) + 1
                stats['successfully_reclassified'] += 1
                
            else:
                print(f"  仍然无法提取有效的检查日期")
                stats['still_undefined'] += 1
                
        except Exception as e:
            print(f"  错误: 处理文件时发生异常: {e}")
            if debug:
                print(traceback.format_exc())
            stats['failed_list'].append((filename, str(e)))
    
    return stats


def print_reprocess_report(stats):
    """
    打印重新处理结果报告
    
    参数:
        stats (dict): 处理统计结果
    """
    print("\n" + "="*50)
    print("重新处理结果报告")
    print("="*50)
    
    print(f"总文件数: {stats['total_files']}")
    print(f"成功重新分类: {stats['successfully_reclassified']}")
    print(f"仍然无法分类: {stats['still_undefined']}")
    
    # 计算成功率
    success_rate = (stats['successfully_reclassified'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    print(f"重新分类成功率: {success_rate:.1f}%")
    
    if stats['year_counts']:
        print("\n重新分类的年份统计:")
        for year in sorted(stats['year_counts'].keys()):
            count = stats['year_counts'][year]
            print(f"  {year}年: {count} 个文件")
    
    if stats['failed_list']:
        print(f"\n处理异常的文件 ({len(stats['failed_list'])} 个):")
        for filename, error in stats['failed_list'][:5]:
            print(f"  - {filename}: {error}")
        if len(stats['failed_list']) > 5:
            print(f"  ... 还有 {len(stats['failed_list']) - 5} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='重新处理undefined文件夹中的文件')
    
    # 默认路径
    default_undefined_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/classified_by_year_final/undefined"
    default_target_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/classified_by_year_final"
    
    parser.add_argument('--undefined-dir', 
                       default=default_undefined_dir,
                       help=f'undefined文件夹路径 (默认: {default_undefined_dir})')
    parser.add_argument('--target-dir',
                       default=default_target_dir, 
                       help=f'目标分类目录路径 (默认: {default_target_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')
    
    args = parser.parse_args()
    
    print("重新处理undefined文件夹中的文件")
    print(f"undefined目录: {args.undefined_dir}")
    print(f"目标目录: {args.target_dir}")
    print("-" * 50)
    
    # 执行重新处理
    stats = reprocess_undefined_files(args.undefined_dir, args.target_dir, args.debug)
    
    if stats:
        print_reprocess_report(stats)
    else:
        print("重新处理失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
