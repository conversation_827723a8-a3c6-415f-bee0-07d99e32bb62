#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用改进的日期提取逻辑重新处理undefined文件

此脚本使用改进的日期提取逻辑重新处理undefined文件夹中的文件。

使用方法:
    python reprocess_undefined_improved.py [--undefined-dir UNDEFINED_DIR] [--target-dir TARGET_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import shutil
import argparse
from pathlib import Path
import traceback

# 导入改进的文档信息提取功能
sys.path.append(os.path.dirname(__file__))
from classify_documents_by_year import extract_date_from_document_content, extract_year_from_date


def reprocess_undefined_with_improved_extraction(undefined_dir, target_dir, debug=False):
    """
    使用改进的日期提取逻辑重新处理undefined文件夹中的文件
    
    参数:
        undefined_dir (str): undefined文件夹路径
        target_dir (str): 目标分类目录
        debug (bool): 是否显示调试信息
        
    返回:
        dict: 重新处理结果统计
    """
    if not os.path.exists(undefined_dir):
        print(f"错误: undefined目录不存在 - {undefined_dir}")
        return None
    
    # 获取所有文件
    files = []
    for ext in ['.docx', '.doc', '.wps']:
        files.extend([f for f in os.listdir(undefined_dir) if f.endswith(ext)])
    
    if not files:
        print(f"在目录 {undefined_dir} 中未找到文件")
        return None
    
    print(f"找到 {len(files)} 个需要重新处理的文件")
    
    # 处理统计
    stats = {
        'total_files': len(files),
        'successfully_reclassified': 0,
        'still_undefined': 0,
        'year_counts': {},
        'failed_list': []
    }
    
    for i, filename in enumerate(files, 1):
        file_path = os.path.join(undefined_dir, filename)
        print(f"[{i}/{len(files)}] 重新处理文件: {filename}")
        
        try:
            # 使用改进的日期提取
            check_date = extract_date_from_document_content(file_path, debug=debug)
            
            if debug:
                print(f"  提取的日期: {check_date}")
            
            # 提取年份
            year = extract_year_from_date(check_date) if check_date else None
            
            if year and check_date:
                # 创建年份目录
                year_dir = os.path.join(target_dir, year)
                if not os.path.exists(year_dir):
                    os.makedirs(year_dir)
                    print(f"  创建年份目录: {year}")
                
                # 移动文件到年份目录
                target_file = os.path.join(year_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(year_dir, new_filename)
                        counter += 1
                    print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.move(file_path, target_file)
                print(f"  移动到: {year}/{os.path.basename(target_file)}")
                
                # 更新统计
                stats['year_counts'][year] = stats['year_counts'].get(year, 0) + 1
                stats['successfully_reclassified'] += 1
                
            else:
                print(f"  仍然无法提取有效的检查日期")
                stats['still_undefined'] += 1
                
        except Exception as e:
            print(f"  错误: 处理文件时发生异常: {e}")
            if debug:
                print(traceback.format_exc())
            stats['failed_list'].append((filename, str(e)))
            stats['still_undefined'] += 1
    
    return stats


def print_improved_reprocess_report(stats):
    """
    打印改进的重新处理结果报告
    
    参数:
        stats (dict): 处理统计结果
    """
    print("\n" + "="*50)
    print("改进的重新处理结果报告")
    print("="*50)
    
    print(f"总文件数: {stats['total_files']}")
    print(f"成功重新分类: {stats['successfully_reclassified']}")
    print(f"仍然无法分类: {stats['still_undefined']}")
    
    # 计算成功率
    success_rate = (stats['successfully_reclassified'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    print(f"重新分类成功率: {success_rate:.1f}%")
    
    if stats['year_counts']:
        print("\n重新分类的年份统计:")
        for year in sorted(stats['year_counts'].keys()):
            count = stats['year_counts'][year]
            print(f"  {year}年: {count} 个文件")
    
    if stats['failed_list']:
        print(f"\n处理异常的文件 ({len(stats['failed_list'])} 个):")
        for filename, error in stats['failed_list'][:5]:
            print(f"  - {filename}: {error}")
        if len(stats['failed_list']) > 5:
            print(f"  ... 还有 {len(stats['failed_list']) - 5} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='使用改进的日期提取逻辑重新处理undefined文件')
    
    # 默认路径
    default_undefined_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/classified_strict/undefined"
    default_target_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/classified_strict"
    
    parser.add_argument('--undefined-dir', 
                       default=default_undefined_dir,
                       help=f'undefined文件夹路径 (默认: {default_undefined_dir})')
    parser.add_argument('--target-dir',
                       default=default_target_dir, 
                       help=f'目标分类目录路径 (默认: {default_target_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')
    
    args = parser.parse_args()
    
    print("使用改进的日期提取逻辑重新处理undefined文件")
    print("现在可以识别文档中的标准日期格式（如2024-10-14）")
    print(f"undefined目录: {args.undefined_dir}")
    print(f"目标目录: {args.target_dir}")
    print("-" * 50)
    
    # 执行改进的重新处理
    stats = reprocess_undefined_with_improved_extraction(args.undefined_dir, args.target_dir, args.debug)
    
    if stats:
        print_improved_reprocess_report(stats)
    else:
        print("改进的重新处理失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
