#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
严格重新分类文档

此脚本使用严格的逻辑重新分类所有文档：
- 只有当文档中有明确的"检查日期"且年份完整时，才进行分类
- 否则放入undefined文件夹

使用方法:
    python strict_reclassify.py [--source-dir SOURCE_DIR] [--target-dir TARGET_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import shutil
import argparse
from pathlib import Path
import traceback

# 导入改进的文档信息提取功能
sys.path.append(os.path.dirname(__file__))
from classify_documents_by_year import extract_date_from_document_content, extract_year_from_date


def strict_extract_date(file_path, debug=False):
    """
    严格提取检查日期 - 只接受明确标注为"检查日期"的完整日期
    
    参数:
        file_path (str): 文件路径
        debug (bool): 是否显示调试信息
        
    返回:
        str: 提取的日期字符串，如果无法提取则返回空字符串
    """
    try:
        # 使用现有的文档内容提取功能
        date = extract_date_from_document_content(file_path, debug=debug)
        
        if not date:
            return ""
        
        # 严格验证：必须包含完整的4位年份
        year_match = re.search(r'\b(\d{4})\b', date)
        if not year_match:
            if debug:
                print(f"  日期格式不完整，缺少4位年份: {date}")
            return ""
        
        year = int(year_match.group(1))
        if not (1990 <= year <= 2030):
            if debug:
                print(f"  年份不在合理范围内: {year}")
            return ""
        
        return date
        
    except Exception as e:
        if debug:
            print(f"  提取日期时发生异常: {e}")
        return ""


def strict_reclassify_all_documents(source_dir, target_dir, debug=False):
    """
    严格重新分类所有文档
    
    参数:
        source_dir (str): 源文件目录
        target_dir (str): 目标分类目录
        debug (bool): 是否显示调试信息
        
    返回:
        dict: 重新分类结果统计
    """
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在 - {source_dir}")
        return None
    
    # 清空目标目录
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    os.makedirs(target_dir)
    
    # 创建undefined目录
    undefined_dir = os.path.join(target_dir, 'undefined')
    os.makedirs(undefined_dir)
    
    # 获取所有文件
    files = []
    for ext in ['.docx', '.doc', '.wps']:
        files.extend([f for f in os.listdir(source_dir) if f.endswith(ext)])
    
    if not files:
        print(f"在目录 {source_dir} 中未找到文件")
        return None
    
    print(f"找到 {len(files)} 个文件需要重新分类")
    
    # 处理统计
    stats = {
        'total_files': len(files),
        'successfully_classified': 0,
        'moved_to_undefined': 0,
        'year_counts': {},
        'failed_list': []
    }
    
    for i, filename in enumerate(files, 1):
        file_path = os.path.join(source_dir, filename)
        print(f"[{i}/{len(files)}] 处理文件: {filename}")
        
        try:
            # 使用严格的日期提取
            check_date = strict_extract_date(file_path, debug=debug)
            
            if debug:
                print(f"  严格提取的日期: {check_date}")
            
            # 提取年份
            year = extract_year_from_date(check_date) if check_date else None
            
            if year and check_date:
                # 创建年份目录
                year_dir = os.path.join(target_dir, year)
                if not os.path.exists(year_dir):
                    os.makedirs(year_dir)
                    print(f"  创建年份目录: {year}")
                
                # 移动文件到年份目录
                target_file = os.path.join(year_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(year_dir, new_filename)
                        counter += 1
                    print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                print(f"  分类到: {year}/{os.path.basename(target_file)}")
                
                # 更新统计
                stats['year_counts'][year] = stats['year_counts'].get(year, 0) + 1
                stats['successfully_classified'] += 1
                
            else:
                # 移动到undefined目录
                target_file = os.path.join(undefined_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(undefined_dir, new_filename)
                        counter += 1
                    print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                print(f"  移动到: undefined/{os.path.basename(target_file)}")
                stats['moved_to_undefined'] += 1
                
        except Exception as e:
            print(f"  错误: 处理文件时发生异常: {e}")
            if debug:
                print(traceback.format_exc())
            stats['failed_list'].append((filename, str(e)))
    
    return stats


def print_strict_reclassify_report(stats):
    """
    打印严格重新分类结果报告
    
    参数:
        stats (dict): 处理统计结果
    """
    print("\n" + "="*50)
    print("严格重新分类结果报告")
    print("="*50)
    
    print(f"总文件数: {stats['total_files']}")
    print(f"成功分类: {stats['successfully_classified']}")
    print(f"移至undefined: {stats['moved_to_undefined']}")
    
    # 计算成功率
    success_rate = (stats['successfully_classified'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    print(f"分类成功率: {success_rate:.1f}%")
    
    if stats['year_counts']:
        print("\n按年份分类统计:")
        for year in sorted(stats['year_counts'].keys()):
            count = stats['year_counts'][year]
            print(f"  {year}年: {count} 个文件")
    
    if stats['failed_list']:
        print(f"\n处理异常的文件 ({len(stats['failed_list'])} 个):")
        for filename, error in stats['failed_list'][:5]:
            print(f"  - {filename}: {error}")
        if len(stats['failed_list']) > 5:
            print(f"  ... 还有 {len(stats['failed_list']) - 5} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='严格重新分类所有文档')
    
    # 默认路径
    default_source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/VNG检查报告2024.11.17"
    default_target_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/classified_strict"
    
    parser.add_argument('--source-dir', 
                       default=default_source_dir,
                       help=f'源文件目录路径 (默认: {default_source_dir})')
    parser.add_argument('--target-dir',
                       default=default_target_dir, 
                       help=f'目标分类目录路径 (默认: {default_target_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')
    
    args = parser.parse_args()
    
    print("严格重新分类所有文档")
    print("只有明确标注'检查日期'且年份完整的文档才会被分类")
    print(f"源目录: {args.source_dir}")
    print(f"目标目录: {args.target_dir}")
    print("-" * 50)
    
    # 执行严格重新分类
    stats = strict_reclassify_all_documents(args.source_dir, args.target_dir, args.debug)
    
    if stats:
        print_strict_reclassify_report(stats)
    else:
        print("严格重新分类失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
